<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
  <view class="info-confirm-page">
    <!-- 内部卡片容器 -->
    <view class="content-card">
      <!-- 顶部圆形图标 -->
      <view class="top-icon">
        <view class="circle-icon"></view>
      </view>

      <!-- 标题和图标 -->
      <view class="title-section">
        <image class="title-icon" src="https://createone.oss-cn-hangzhou.aliyuncs.com/1468808703988994048/weixin/wesmile-ma/message-icon.png"></image>
        <text class="title-text">请确认您的信息</text>
      </view>

      <!-- 绿色提示框 -->
      <view class="info-tip">
        <text class="tip-text">为了精准匹配的专属需求，</text>
        <text class="tip-text">请提供以下信息，</text>
        <text class="tip-text">让我们的服务更贴合您的期待，麻烦啦～</text>
      </view>

    <form @submit="userInfoSave">
      <!-- 电话输入框 -->
      <view class="form-item">
        <view class="form-row">
          <view class="form-label">
            <text class="label-text">电话</text>
            <text class="required">*（必填）</text>
          </view>
          <view class="form-input" @click="showPhoneModal">
            <input
              type="text"
              placeholder="13799376646"
              :value="userInfo.phone"
              disabled
            />
          </view>
        </view>
        <view class="form-line"></view>
      </view>

      <!-- 昵称输入框 -->
      <view class="form-item">
        <view class="form-row">
          <view class="form-label">
            <text class="label-text">昵称</text>
            <text class="required">*（必填）</text>
          </view>
          <view class="form-input">
            <input
              type="nickname"
              placeholder="奇门遁甲"
              name="nickName"
              :value="userInfo.nickName"
            />
          </view>
        </view>
        <view class="form-line"></view>
      </view>

      <!-- 头像上传 -->
      <view class="form-item">
        <view class="form-row">
          <view class="form-label">
            <text class="label-text">头像</text>
            <text class="required">*（必填）</text>
          </view>
          <view class="avatar-upload">
            <!-- #ifdef MP -->
            <button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
              <image class="avatar-image" :src="userInfo.headimgUrl || '/static/public/logo.png'"></image>
            </button>
            <!-- #endif -->
            <!-- #ifndef MP -->
            <view class="avatar-image"
                  :style="'background-image:url(' + (userInfo.headimgUrl || '/static/public/logo.png') + ');'"
                  @click="chooseImage">
            </view>
            <!-- #endif -->
          </view>
        </view>
        <view class="form-line"></view>

        <!-- 性别选择 - 放在头像底下的线底下 -->
        <view class="gender-section-inline">
          <radio-group @change="radioChange">
            <view class="gender-options-inline">
              <view class="gender-option-inline" :class="{ active: userInfo.sex === '0' || userInfo.sex === '' }">
                <view class="gender-radio-inline">
                  <radio value="0" :checked="userInfo.sex === '0' || userInfo.sex === ''"></radio>
                  <!-- 使用指定的勾选图标 -->
                  <image v-if="userInfo.sex === '0' || userInfo.sex === ''" class="check-icon-inline" src="https://createone.oss-cn-hangzhou.aliyuncs.com/1468808703988994048/weixin/wesmile-ma/check.png"></image>
                </view>
                <text class="gender-text-inline">女</text>
              </view>

              <view class="gender-option-inline" :class="{ active: userInfo.sex === '1' }">
                <view class="gender-radio-inline">
                  <radio value="1" :checked="userInfo.sex === '1'"></radio>
                  <!-- 使用指定的勾选图标 -->
                  <image v-if="userInfo.sex === '1'" class="check-icon-inline" src="https://createone.oss-cn-hangzhou.aliyuncs.com/1468808703988994048/weixin/wesmile-ma/check.png"></image>
                </view>
                <text class="gender-text-inline">男</text>
              </view>
            </view>
          </radio-group>
        </view>
      </view>

    </form>

    <!-- 确认按钮 - 固定在卡片底部 -->
    <view class="confirm-section">
      <button class="confirm-btn" formType="submit">
        <text class="btn-text">确认并支付</text>
      </button>
    </view>
    </view> <!-- 关闭内部卡片容器 -->
    <view class="cu-modal" :class="phoneModal ? ' show' : ''" style="z-index: 10 !important">
      <view class="cu-dialog">
        <view class="cu-bar bg-white justify-end">
          <view class="content">绑定手机号</view>
          <view class="action" @click="hidePhoneModal">
            <text class="cuIcon-close text-red"></text>
          </view>
        </view>
        <view class="padding bg-white">
          <view class="text-center margin-bottom">请先绑定手机号，方便客服联系您确认档期等</view>
          <view class="padding flex flex-direction">
            <!-- #ifdef MP-WEIXIN -->
            <button class="cu-btn margin-top-sm round lg" :class="'bg-' + theme.themeColor" open-type="getPhoneNumber"
                    @getphonenumber="getPhoneNumber">确认
            </button>
            <!-- #endif -->
            <!-- #ifndef MP-WEIXIN -->
            <button class="cu-btn margin-top-sm round lg" :class="'bg-' + theme.themeColor" @click="hidePhoneModal">
              确认
            </button>
            <!-- #endif -->
          </view>
        </view>
      </view>
    </view>
    <!-- 发送短信之前的图形验证码 -->
  </view>
</template>

<script>
/**
 * Copyright (C) 2025
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
const app = getApp();
const util = require('utils/util.js');
import api from 'utils/api';
import validate from 'utils/validate';
import __config from 'config/env';

const MSGINIT = '发送验证码',
    MSGSCUCCESS = '${time}秒后可重发',
    MSGTIME = 60;
export default {
  components: {},
  data() {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      userInfo: {},
      phoneModal: false,
      form: {},
      msgText: MSGINIT,
      msgTime: MSGTIME,
      msgKey: false
    };
  },
  onLoad(options) {
    if (options.pay){
      uni.showToast({
        title: '请先完善个人信息,以便后续客服联系',
        icon: 'none',
        duration: 2000
      });
    }
    this.userInfoGet();
  },
  methods: {
    // 显示图形验证码弹框
    // #ifdef MP-WEIXIN
    /**
     * 小程序设置
     */
    settings: function () {
      uni.openSetting({
        success: function (res) {
          console.log(res.authSetting);
        }
      });
    },
    // #endif
    //获取商城用户信息
    userInfoGet() {
      api.userInfoGet().then((res) => {
        this.userInfo = res.data;
        if (!res.data.sex) {
          this.userInfo.sex = ''
        }
      });
    },
    radioChange(e) {
      this.userInfo.sex = e.detail.value;
      // 强制更新视图
      this.$forceUpdate();
    },
    uploadAvatar(filePath) {
      // 上传头像
      let that = this;
      api.uploadFile(filePath, 'headimg/', 'image')
          .then((link) => {
            that.userInfo.headimgUrl = link;
          })
          .catch((e) => {
          });
    },
    onChooseAvatar(e) {
      const {avatarUrl} = e.detail;
      this.uploadAvatar(avatarUrl);
    },
    //选择头像上传
    chooseImage() {
      uni.chooseImage({
        success: (chooseImageRes) => {
          const tempFilePaths = chooseImageRes.tempFilePaths;
          this.uploadAvatar(tempFilePaths[0]);
        }
      });
    },
    userInfoSave(e) {
      let value = e.detail.value;
      if (!this.userInfo.phone) {
        uni.showToast({
          title: '请绑定手机号',
          icon: 'none',
          duration: 3000
        });
        this.showPhoneModal()
        return;
      }
      if (!this.userInfo.headimgUrl) {
        uni.showToast({
          title: '请上传头像',
          icon: 'none',
          duration: 3000
        });
        return;
      }
      if (!value.nickName) {
        uni.showToast({
          title: '请填写昵称',
          icon: 'none',
          duration: 3000
        });
        return;
      }
      if (this.userInfo.sex === null || this.userInfo.sex === undefined) {
        // 如果没有选择性别，默认设置为女性
        this.userInfo.sex = "0";
      }
      api.userInfoUpdate({
        id: this.userInfo.id,
        nickName: value.nickName,
        sex: this.userInfo.sex,
        headimgUrl: this.userInfo.headimgUrl
      }).then((res) => {
        uni.setStorageSync('user_info', res.data);
        //更新一下im个人资料
        uni.navigateBack({
          delta: 1
        });
      });
    },
    showPhoneModal() {
      this.phoneModal = true;
    },
    hidePhoneModal() {
      this.phoneModal = false;
    },
    getPhoneCode(graphCode) {
      if (this.msgKey) return;
      if (this.form.phone == this.userInfo.phone) {
        uni.showToast({
          title: '输入号码与当前绑定号码相同',
          icon: 'none',
          duration: 3000
        });
        return;
      }
      if (!validate.validateMobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号码',
          icon: 'none',
          duration: 3000
        });
        return;
      }
      if (!graphCode) {
        uni.showToast({
          title: '请输入图形验证码',
          icon: 'none',
          duration: 3000
        });
        return;
      }
      this.msgKey = true;
      api.getPhoneCode({
        type: '2',
        phone: this.form.phone,
        graphCode
      })
          .then((res) => {
            this.msgKey = false;
            if (res.code == '0') {
              uni.showToast({
                title: '验证码发送成功',
                icon: 'none',
                duration: 3000
              });
              this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime);
              this.msgKey = true;
              const time = setInterval(() => {
                this.msgTime--;
                this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime);
                if (this.msgTime == 0) {
                  this.msgTime = MSGTIME;
                  this.msgText = MSGINIT;
                  this.msgKey = false;
                  clearInterval(time);
                }
              }, 1000);
              this.$refs.graphCodeRef.hideModal();
            } else {
              this.$refs.graphCodeRef.refreshGraphCode();
            }
          })
          .catch(() => {
            this.msgKey = false;
            this.$refs.graphCodeRef.refreshGraphCode();
          });
    },
    getPhoneNumber(e) {
      if (e.detail.errMsg === 'getPhoneNumber:ok') {
        // 获取手机号成功
        api.loginByPhoneMa(e.detail).then(res => {
            // 更新用户手机号
            this.userInfo.phone = res.data.phone;
            uni.showToast({
              title: '手机号绑定成功',
              icon: 'success',
              duration: 2000
            });
            this.hidePhoneModal();

        }).catch((err) => {
          console.log(err)
          uni.showToast({
            title: '手机号获取失败',
            icon: 'none',
            duration: 2000
          });
        });
      } else {
        uni.showToast({
          title: '您取消了授权',
          icon: 'none',
          duration: 2000
        });
      }
    }
  }
};
</script>

<style>
.info-confirm-page {
  min-height: 100vh;
  background-color: white; /* 最外层白色背景 */
  padding: 0;
  box-sizing: border-box;
  padding-top: 63rpx;
}

/* 内部卡片容器 */
.content-card {
  position: relative; /* 为顶部圆球和底部按钮定位提供参考 */
  background-color: #eaeaea; /* 内部卡片背景 */
  margin-left: 38rpx; /* 距离左侧38rpx */
  margin-right: 38rpx; /* 右侧也保持38rpx对称 */
  padding: 0 40rpx;
  padding-top: 65rpx;
  padding-bottom: 120rpx; /* 为底部固定按钮留出空间 */
  height: 100%; /* 调整高度以适应顶部间距 */
  box-sizing: border-box;
  border-radius: 20rpx;
}

/* 顶部圆形图标 */
.top-icon {
  position: absolute;
  top: -40rpx; /* 浮动在卡片顶部中间位置 */
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

.circle-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: #81958f;
  border-radius: 50%;
}

/* 标题区域 */
.title-section {
  display: flex;
  align-items: center;
  justify-content: center; /* 居中对齐 */
  margin-bottom: 43rpx; /* 距离底下43rpx */
}

.title-icon {
  width: 48rpx; /* 设置图标宽度 */
  height: 48rpx; /* 设置图标高度 */
  margin-right: 20rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

/* 提示框 */
.info-tip {
  background-color: #81958f;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 60rpx;
  text-align: center;
}

.tip-text {
  display: block;
  color: white;
  font-size: 28rpx;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.tip-text:last-child {
  margin-bottom: 0;
}

/* 表单项 */
.form-item {
  margin-bottom: 56rpx;
}

.form-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 56rpx;
}

.form-label {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.label-text {
  font-size: 32rpx;
  color: #363636;
  margin-right: 10rpx;
}

.required {
  font-size: 24rpx;
  color: #bcbcbc;
}

.form-input {
  flex: 1;
  text-align: right;
}

.form-input input {
  width: 100%;
  font-size: 32rpx;
  color: #363636;
  border: none;
  outline: none;
  background: transparent;
  text-align: right;
}

.form-input input::placeholder {
  color: #999;
}

.form-line {
  height: 2rpx;
  background-color: #363636;
}

/* 头像上传 */
.avatar-upload {
  flex-shrink: 0;
  position: relative;
  height: 32rpx; /* 与label文字高度一致 */
  display: flex;
  align-items: center;
}

.avatar-wrapper {
  background-color: transparent;
  border: none;
  padding: 0;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

.avatar-wrapper::after {
  border-style: none;
}

.avatar-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 12rpx;
  background-color: #f0f0f0;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 性别选择 - inline版本 */
.gender-section-inline {
  margin-top: 40rpx;
  display: flex;
  justify-content: flex-end; /* 整体右对齐 */
}

.gender-options-inline {
  display: flex;
  align-items: center;
  gap: 60rpx;
}

.gender-option-inline {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.gender-radio-inline {
  position: relative;
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  flex-shrink: 0;
}

.gender-option-inline.active .gender-radio-inline {
  border-color: #81958f;
  background-color: #81958f;
}

.gender-radio-inline radio {
  opacity: 0;
  position: absolute;
  width: 100%;
  height: 100%;
}

.gender-text-inline {
  font-size: 28rpx;
  color: #333;
}

.check-icon-inline {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24rpx; /* 设置图标宽度 */
  height: 24rpx; /* 设置图标高度 */
}

/* 确认按钮 */
.confirm-section {
  position: absolute;
  bottom: -50rpx; /* 固定在卡片底部外侧 */
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  width: calc(100% - 80rpx); /* 保持按钮宽度，减去左右padding */
}

.confirm-btn {
  width: 100%;
  height: 100rpx;
  background-image: url('https://createone.oss-cn-hangzhou.aliyuncs.com/1468808703988994048/weixin/wesmile-ma/button-check.png');
  background-size: contain;
  background-color: transparent; /* 设置背景色为透明 */
  background-position: center;
  background-repeat: no-repeat;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirm-btn::after {
  border: none;
}

.btn-text {
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  margin-right: -50rpx;
}
</style>
